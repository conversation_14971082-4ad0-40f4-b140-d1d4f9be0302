# TrueWorth — Bangladesh

**See the real value of your earnings after inflation**

TrueWorth is a web application that helps you understand whether your income — from salary, freelancing, or business — is truly growing or silently shrinking due to inflation. Using official CPI inflation data for Bangladesh, it calculates how much your earnings should have increased to maintain the same purchasing power and shows whether you're in profit or loss in real terms.

## 🌟 Features

- **Real Salary Growth Calculator**: Compare your salary growth against inflation to see your true financial progress
- **Bilingual Support**: Full support for English and Bengali (বাংলা) languages
- **Dark/Light Theme**: Toggle between dark and light modes for comfortable viewing
- **Interactive Charts**: Visual representation of salary comparison using Recharts
- **Share Results**: Download your results as PNG images to share with others
- **Mobile Responsive**: Optimized for all device sizes
- **Official Data**: Uses Bangladesh Bureau of Statistics, Bangladesh Bank, and World Bank CPI data

## 🚀 Live Demo

Visit the live application: [https://trueworth.mamunhq.com](https://trueworth.mamunhq.com)

## 🛠️ Tech Stack

- **Frontend Framework**: React 18 with TypeScript
- **Build Tool**: Vite 6
- **Styling**: Tailwind CSS 3
- **UI Components**: Headless UI for accessible components
- **Charts**: Recharts for data visualization
- **Icons**: Lucide React
- **Image Export**: html2canvas for PNG generation
- **Utilities**: clsx for conditional styling

## 📊 How It Works

1. **Input Your Data**: Enter your base year salary and current year salary
2. **Inflation Calculation**: The app applies month-by-month inflation rates from official Bangladesh data
3. **Real Growth Analysis**: Compares your actual salary growth with inflation-adjusted requirements
4. **Results Display**: Shows whether you're in profit or loss in real purchasing power terms

### Calculation Method

The application uses a precise month-by-month calculation:
- Takes your base salary from a specific month/year
- Applies cumulative inflation for each month until the current date
- Calculates the required salary to maintain purchasing power
- Compares with your actual current salary to determine real gain/loss

## 🏗️ Project Structure

```
src/
├── components/          # React components
│   ├── Header.tsx      # App header with theme/language toggles
│   ├── SalaryForm.tsx  # Salary input form
│   ├── Results.tsx     # Results display
│   ├── SalaryChart.tsx # Chart visualization
│   ├── ShareCard.tsx   # Share/download functionality
│   └── InflationDataDisplay.tsx # Inflation data table
├── hooks/              # Custom React hooks
│   ├── useTheme.ts     # Theme management
│   └── useLanguage.ts  # Language switching
├── types/              # TypeScript type definitions
│   └── index.ts        # Main types and interfaces
├── utils/              # Utility functions
│   ├── calculations.ts # Salary calculation logic
│   ├── inflationData.ts # Bangladesh inflation data
│   ├── translations.ts # Bilingual text content
│   └── analytics.ts    # Analytics utilities
├── App.tsx             # Main application component
└── main.tsx           # Application entry point
```

## 🚀 Getting Started

### Prerequisites

- Node.js 18+ 
- npm, yarn, or pnpm

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/MamunHoque/trueworth.git
   cd trueworth
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or
   yarn install
   # or
   pnpm install
   ```

3. **Start development server**
   ```bash
   npm run dev
   # or
   yarn dev
   # or
   pnpm dev
   ```

4. **Open your browser**
   Navigate to `http://localhost:5173`

### Build for Production

```bash
npm run build
# or
yarn build
# or
pnpm build
```

The built files will be in the `dist/` directory.

## 📈 Inflation Data

The application includes official inflation data for Bangladesh:

- **2020**: 5.7%
- **2021**: 5.5%
- **2022**: 7.7%
- **2023**: 9.9%
- **2024**: 10.47%
- **2025**: 8.5% (Projected)

Data sources: Bangladesh Bureau of Statistics, Bangladesh Bank, World Bank CPI data

## 🌐 Internationalization

The app supports two languages:
- **English**: Default language
- **Bengali (বাংলা)**: Complete translation including UI and content

Language switching is available in the header, and the selected language persists across sessions.

## 🎨 Theming

- **Light Mode**: Clean, bright interface
- **Dark Mode**: Easy on the eyes for low-light environments
- Theme preference is saved locally and persists across sessions

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request. For major changes, please open an issue first to discuss what you would like to change.

### Development Guidelines

1. Follow TypeScript best practices
2. Use Tailwind CSS for styling
3. Ensure components are accessible
4. Add proper type definitions
5. Test on both light and dark themes
6. Verify bilingual support works correctly

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 👨‍💻 Author

**Mamun Hoque**
- GitHub: [@MamunHoque](https://github.com/MamunHoque)
- Website: [https://trueworth.mamunhq.com](https://trueworth.mamunhq.com)

## 🙏 Acknowledgments

- Bangladesh Bureau of Statistics for inflation data
- Bangladesh Bank for economic indicators
- World Bank for CPI data
- The React and Vite communities for excellent tooling

---

Built with ❤️ for accurate salary inflation analysis in Bangladesh
