export interface InflationData {
  year: number;
  annual_inflation_pct: number;
  note?: string;
}

export interface SalaryInput {
  baseYear: number;
  baseMonth: number;
  baseSalary: number;
  currentYear: number;
  currentMonth: number;
  currentSalary: number;
}

export interface CalculationResult {
  requiredSalary: number;
  realGainLoss: number;
  realGainLossPercent: number;
  isProfit: boolean;
}

export interface Translation {
  appTitle: string;
  subtitle: string;
  baseYear: string;
  baseSalary: string;
  currentYear: string;
  currentSalary: string;
  calculate: string;
  results: string;
  requiredSalary: string;
  actualSalary: string;
  realGain: string;
  realLoss: string;
  profit: string;
  loss: string;
  purchasingPower: string;
  increased: string;
  decreased: string;
  youAreIn: string;
  inflationData: string;
  dataSource: string;
  shareResults: string;
  downloadPNG: string;
  darkMode: string;
  lightMode: string;
  language: string;
  enterAmount: string;
  selectYear: string;
  chartTitle: string;
  baseYearSalary: string;
  inflationAdjusted: string;
  currentActual: string;
}

export type Language = 'en' | 'bn';
export type Theme = 'light' | 'dark';