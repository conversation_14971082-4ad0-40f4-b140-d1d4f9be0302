export interface AdminUser {
  id: string;
  username: string;
  email: string;
  password_hash: string;
  created_at: string;
  last_login?: string;
  is_active: boolean;
}

export interface UserSession {
  id: string;
  session_id: string;
  ip_address?: string;
  user_agent?: string;
  device_info?: DeviceInfo;
  created_at: string;
  updated_at: string;
  is_active: boolean;
}

export interface AnalyticsEvent {
  id: string;
  session_id: string;
  event_type: 'page_view' | 'calculation' | 'form_submit' | 'share' | 'download';
  event_data?: any;
  user_inputs?: SalaryCalculationData;
  timestamp: string;
  ip_address?: string;
  user_agent?: string;
  referrer?: string;
}

export interface DeviceInfo {
  browser: string;
  os: string;
  device_type: 'desktop' | 'mobile' | 'tablet';
  screen_resolution?: string;
  language: string;
  timezone: string;
}

export interface SalaryCalculationData {
  base_year: number;
  base_month: number;
  base_salary: number;
  current_year: number;
  current_month: number;
  current_salary: number;
  result?: {
    required_salary: number;
    real_gain_loss: number;
    real_gain_loss_percent: number;
    is_profit: boolean;
  };
}

export interface AnalyticsSummary {
  total_sessions: number;
  total_calculations: number;
  total_page_views: number;
  unique_visitors: number;
  avg_session_duration: number;
  top_countries: Array<{ country: string; count: number }>;
  device_breakdown: Array<{ device_type: string; count: number }>;
  browser_breakdown: Array<{ browser: string; count: number }>;
  hourly_activity: Array<{ hour: number; count: number }>;
  daily_activity: Array<{ date: string; count: number }>;
}

export interface LoginAttempt {
  id: string;
  ip_address: string;
  username: string;
  success: boolean;
  timestamp: string;
  user_agent?: string;
}