import React, { useState } from 'react';
import { SalaryInput, CalculationResult } from './types';
import { calculateRealSalaryGrowth } from './utils/calculations';
import { useTheme } from './hooks/useTheme';
import { useLanguage } from './hooks/useLanguage';

import { Header } from './components/Header';
import { SalaryForm } from './components/SalaryForm';
import { InflationDataDisplay } from './components/InflationDataDisplay';
import { Results } from './components/Results';
import { SalaryChart } from './components/SalaryChart';
import { ShareCard } from './components/ShareCard';

function App() {
  const { theme, toggleTheme } = useTheme();
  const { language, toggleLanguage, t } = useLanguage();

  // Get current date for defaults
  const currentDate = new Date();
  const currentYear = currentDate.getFullYear();
  const currentMonth = currentDate.getMonth() + 1; // getMonth() returns 0-11
  const previousYear = currentYear - 1;

  const [input, setInput] = useState<SalaryInput>({
    baseYear: previousYear,
    baseMonth: 1, // January
    baseSalary: 0,
    currentYear: currentYear,
    currentMonth: currentMonth,
    currentSalary: 0
  });

  const [result, setResult] = useState<CalculationResult | null>(null);

  const handleCalculate = () => {
    const calculationResult = calculateRealSalaryGrowth(input);
    setResult(calculationResult);
  };

  const isFormValid = input.baseYear && input.baseSalary > 0 && input.currentYear && input.currentSalary > 0;

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-200">
      <Header
        theme={theme}
        toggleTheme={toggleTheme}
        language={language}
        toggleLanguage={toggleLanguage}
        title={t.appTitle}
        subtitle={t.subtitle}
        darkModeText={t.darkMode}
        lightModeText={t.lightMode}
        languageText={t.language}
      />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-8">
          {/* Inflation Data Display */}
          <InflationDataDisplay
            translations={{
              inflationData: t.inflationData,
              dataSource: t.dataSource
            }}
          />

          {/* Salary Input Form */}
          <SalaryForm
            input={input}
            setInput={setInput}
            onCalculate={handleCalculate}
            translations={{
              baseYear: t.baseYear,
              baseSalary: t.baseSalary,
              currentYear: t.currentYear,
              currentSalary: t.currentSalary,
              calculate: t.calculate,
              enterAmount: t.enterAmount,
              selectYear: t.selectYear
            }}
          />

          {/* Results Section */}
          {result && isFormValid && (
            <div className="space-y-8 mt-12">
              <Results
                result={result}
                input={input}
                translations={{
                  results: t.results,
                  requiredSalary: t.requiredSalary,
                  actualSalary: t.actualSalary,
                  realGain: t.realGain,
                  realLoss: t.realLoss,
                  profit: t.profit,
                  loss: t.loss,
                  purchasingPower: t.purchasingPower,
                  increased: t.increased,
                  decreased: t.decreased,
                  youAreIn: t.youAreIn
                }}
              />

              <SalaryChart
                result={result}
                input={input}
                translations={{
                  chartTitle: t.chartTitle,
                  baseYearSalary: t.baseYearSalary,
                  inflationAdjusted: t.inflationAdjusted,
                  currentActual: t.currentActual
                }}
              />

              <ShareCard
                result={result}
                input={input}
                translations={{
                  shareResults: t.shareResults,
                  downloadPNG: t.downloadPNG,
                  appTitle: t.appTitle,
                  requiredSalary: t.requiredSalary,
                  actualSalary: t.actualSalary,
                  realGain: t.realGain,
                  realLoss: t.realLoss,
                  profit: t.profit,
                  loss: t.loss
                }}
              />
            </div>
          )}
        </div>
      </main>

      {/* About Section */}
      <section className="bg-gray-50 dark:bg-gray-800 py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-6">
            <div>
              <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                TrueWorth — Bangladesh
              </h2>
              <p className="text-xl text-gray-600 dark:text-gray-300 mb-6">
                See the real value of your earnings after inflation.
              </p>
            </div>
            
            <div className="space-y-8 text-left">
              {/* Conditional Language-Specific Content */}
              {language === 'en' ? (
                /* English Description */
                <div>
                  <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                    TrueWorth helps you understand whether your income — from salary, freelancing, or business — is truly growing or silently shrinking. Using official CPI inflation data for Bangladesh, it calculates how much your earnings should have increased to keep the same purchasing power, compares it to your actual growth, and shows whether you're in profit or loss in real terms.
                  </p>
                  <p className="text-gray-700 dark:text-gray-300 leading-relaxed mt-4">
                    With an easy-to-use interface, bilingual support, and clear charts, TrueWorth gives you the insight you need to make smarter salary negotiations, set fair prices, and plan your finances with confidence.
                  </p>
                </div>
              ) : (
                /* Bengali Description */
                <div>
                  <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                    ট্রুওয়ার্থ — বাংলাদেশ
                  </h3>
                  <p className="text-lg text-gray-600 dark:text-gray-300 mb-4">
                    মুদ্রাস্ফীতির পরে আপনার আয়ের আসল মান জানুন
                  </p>
                  <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                    ট্রুওয়ার্থ আপনাকে সহজভাবে বুঝতে সাহায্য করবে আপনার আয় — চাকরি, ফ্রিল্যান্সিং বা ব্যবসা — আসলে বেড়েছে নাকি লুকিয়ে কমেছে। আমরা বাংলাদেশের সরকারি সিপিআই মুদ্রাস্ফীতির তথ্য ব্যবহার করে হিসাব করি, আপনার আয় কত বাড়া উচিত ছিল একই ক্রয়ক্ষমতা রাখতে, এবং সেটি আপনার বর্তমান আয়ের সাথে মিলিয়ে জানাই আপনি লাভে আছেন নাকি ক্ষতিতে।
                  </p>
                  <p className="text-gray-700 dark:text-gray-300 leading-relaxed mt-4">
                    সহজ ডিজাইন, বাংলা ও ইংরেজি সাপোর্ট এবং পরিষ্কার চার্টের মাধ্যমে ট্রুওয়ার্থ আপনাকে বেতন আলোচনা, পণ্যের দাম নির্ধারণ এবং নিজের আর্থিক পরিকল্পনা করতে আরও আত্মবিশ্বাসী করে তুলবে।
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 mt-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="text-center text-sm text-gray-600 dark:text-gray-400">
            <p>
              Data sources: Bangladesh Bureau of Statistics, Bangladesh Bank, World Bank CPI data
            </p>
            <p className="mt-1">
              Built with ❤️ for accurate salary inflation analysis in Bangladesh by{' '}
              <a 
                href="https://github.com/MamunHoque" 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-blue-600 dark:text-blue-400 hover:underline font-medium"
              >
                Mamun Hoque
              </a>
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}

export default App;