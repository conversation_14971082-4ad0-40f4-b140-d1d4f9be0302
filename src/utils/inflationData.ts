import { InflationData } from '../types';

export const inflationData: InflationData[] = [
  { year: 2020, annual_inflation_pct: 5.7 },
  { year: 2021, annual_inflation_pct: 5.5 },
  { year: 2022, annual_inflation_pct: 7.7 },
  { year: 2023, annual_inflation_pct: 9.9 },
  { year: 2024, annual_inflation_pct: 10.47 },
  { year: 2025, annual_inflation_pct: 8.5, note: 'Projected (~8–9% latest guidance)' }
];

export const getInflationDataByYear = (year: number): InflationData | undefined => {
  return inflationData.find(data => data.year === year);
};

export const getAvailableYears = (): number[] => {
  return inflationData.map(data => data.year).sort((a, b) => a - b);
};