import { createClient } from '@supabase/supabase-js';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables');
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Database types
export type Database = {
  public: {
    Tables: {
      admin_users: {
        Row: {
          id: string;
          username: string;
          email: string;
          password_hash: string;
          created_at: string;
          last_login: string | null;
          is_active: boolean;
        };
        Insert: {
          id?: string;
          username: string;
          email: string;
          password_hash: string;
          created_at?: string;
          last_login?: string | null;
          is_active?: boolean;
        };
        Update: {
          id?: string;
          username?: string;
          email?: string;
          password_hash?: string;
          created_at?: string;
          last_login?: string | null;
          is_active?: boolean;
        };
      };
      user_sessions: {
        Row: {
          id: string;
          session_id: string;
          ip_address: string | null;
          user_agent: string | null;
          device_info: any | null;
          created_at: string;
          updated_at: string;
          is_active: boolean;
        };
        Insert: {
          id?: string;
          session_id: string;
          ip_address?: string | null;
          user_agent?: string | null;
          device_info?: any | null;
          created_at?: string;
          updated_at?: string;
          is_active?: boolean;
        };
        Update: {
          id?: string;
          session_id?: string;
          ip_address?: string | null;
          user_agent?: string | null;
          device_info?: any | null;
          created_at?: string;
          updated_at?: string;
          is_active?: boolean;
        };
      };
      analytics_events: {
        Row: {
          id: string;
          session_id: string;
          event_type: string;
          event_data: any | null;
          user_inputs: any | null;
          timestamp: string;
          ip_address: string | null;
          user_agent: string | null;
          referrer: string | null;
        };
        Insert: {
          id?: string;
          session_id: string;
          event_type: string;
          event_data?: any | null;
          user_inputs?: any | null;
          timestamp?: string;
          ip_address?: string | null;
          user_agent?: string | null;
          referrer?: string | null;
        };
        Update: {
          id?: string;
          session_id?: string;
          event_type?: string;
          event_data?: any | null;
          user_inputs?: any | null;
          timestamp?: string;
          ip_address?: string | null;
          user_agent?: string | null;
          referrer?: string | null;
        };
      };
      login_attempts: {
        Row: {
          id: string;
          ip_address: string;
          username: string;
          success: boolean;
          timestamp: string;
          user_agent: string | null;
        };
        Insert: {
          id?: string;
          ip_address: string;
          username: string;
          success: boolean;
          timestamp?: string;
          user_agent?: string | null;
        };
        Update: {
          id?: string;
          ip_address?: string;
          username?: string;
          success?: boolean;
          timestamp?: string;
          user_agent?: string | null;
        };
      };
    };
  };
};