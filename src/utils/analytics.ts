import { supabase } from './supabase';
import { AnalyticsEvent, UserSession, DeviceInfo, SalaryCalculationData } from '../types/admin';

class AnalyticsService {
  private sessionId: string;
  private sessionStartTime: number;

  constructor() {
    this.sessionId = this.generateSessionId();
    this.sessionStartTime = Date.now();
    this.initializeSession();
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private async getClientIP(): Promise<string | null> {
    try {
      const response = await fetch('https://api.ipify.org?format=json');
      const data = await response.json();
      return data.ip;
    } catch (error) {
      console.warn('Could not fetch IP address:', error);
      return null;
    }
  }

  private getDeviceInfo(): DeviceInfo {
    const userAgent = navigator.userAgent;
    const screen = window.screen;
    
    // Simple browser detection
    let browser = 'Unknown';
    if (userAgent.includes('Chrome')) browser = 'Chrome';
    else if (userAgent.includes('Firefox')) browser = 'Firefox';
    else if (userAgent.includes('Safari')) browser = 'Safari';
    else if (userAgent.includes('Edge')) browser = 'Edge';

    // Simple OS detection
    let os = 'Unknown';
    if (userAgent.includes('Windows')) os = 'Windows';
    else if (userAgent.includes('Mac')) os = 'macOS';
    else if (userAgent.includes('Linux')) os = 'Linux';
    else if (userAgent.includes('Android')) os = 'Android';
    else if (userAgent.includes('iOS')) os = 'iOS';

    // Device type detection
    let deviceType: 'desktop' | 'mobile' | 'tablet' = 'desktop';
    if (/Mobi|Android/i.test(userAgent)) deviceType = 'mobile';
    else if (/Tablet|iPad/i.test(userAgent)) deviceType = 'tablet';

    return {
      browser,
      os,
      device_type: deviceType,
      screen_resolution: `${screen.width}x${screen.height}`,
      language: navigator.language,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
    };
  }

  private async initializeSession(): Promise<void> {
    try {
      const ip = await this.getClientIP();
      const deviceInfo = this.getDeviceInfo();

      const sessionData: UserSession = {
        id: crypto.randomUUID(),
        session_id: this.sessionId,
        ip_address: ip,
        user_agent: navigator.userAgent,
        device_info: deviceInfo,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        is_active: true
      };

      await supabase
        .from('user_sessions')
        .insert(sessionData);

      // Track page view
      this.trackEvent('page_view', {
        page: window.location.pathname,
        referrer: document.referrer
      });

    } catch (error) {
      console.warn('Analytics initialization failed:', error);
    }
  }

  async trackEvent(
    eventType: AnalyticsEvent['event_type'],
    eventData?: any,
    userInputs?: SalaryCalculationData
  ): Promise<void> {
    try {
      const ip = await this.getClientIP();

      const analyticsEvent: Omit<AnalyticsEvent, 'id'> = {
        session_id: this.sessionId,
        event_type: eventType,
        event_data: eventData,
        user_inputs: userInputs,
        timestamp: new Date().toISOString(),
        ip_address: ip,
        user_agent: navigator.userAgent,
        referrer: document.referrer
      };

      await supabase
        .from('analytics_events')
        .insert(analyticsEvent);

    } catch (error) {
      console.warn('Event tracking failed:', error);
    }
  }

  async trackCalculation(inputData: SalaryCalculationData): Promise<void> {
    await this.trackEvent('calculation', {
      calculation_type: 'salary_inflation',
      years_span: inputData.current_year - inputData.base_year
    }, inputData);
  }

  async trackShare(method: 'native' | 'clipboard'): Promise<void> {
    await this.trackEvent('share', { method });
  }

  async trackDownload(type: 'png'): Promise<void> {
    await this.trackEvent('download', { type });
  }

  async endSession(): Promise<void> {
    try {
      const sessionDuration = Date.now() - this.sessionStartTime;
      
      await supabase
        .from('user_sessions')
        .update({
          is_active: false,
          updated_at: new Date().toISOString()
        })
        .eq('session_id', this.sessionId);

      await this.trackEvent('session_end', {
        duration: sessionDuration
      });

    } catch (error) {
      console.warn('Session end tracking failed:', error);
    }
  }
}

// Create singleton instance
export const analytics = new AnalyticsService();

// Track page unload
window.addEventListener('beforeunload', () => {
  analytics.endSession();
});