import { SalaryInput, CalculationResult } from '../types';
import { getInflationDataByYear } from './inflationData';

export const calculateRealSalaryGrowth = (input: SalaryInput): CalculationResult => {
  const { baseYear, baseMonth, baseSalary, currentYear, currentMonth, currentSalary } = input;
  
  let requiredSalary = baseSalary;
  
  // Calculate total months between base and current dates
  const baseDate = new Date(baseYear, baseMonth - 1);
  const currentDate = new Date(currentYear, currentMonth - 1);
  
  // Apply cumulative inflation month by month for more precision
  let currentCalcDate = new Date(baseDate);
  
  while (currentCalcDate < currentDate) {
    currentCalcDate.setMonth(currentCalcDate.getMonth() + 1);
    const year = currentCalcDate.getFullYear();
    
    const inflationData = getInflationDataByYear(year);
    if (inflationData) {
      // Apply monthly inflation rate (annual rate / 12)
      const monthlyInflationRate = (inflationData.annual_inflation_pct / 100) / 12;
      requiredSalary = requiredSalary * (1 + monthlyInflationRate);
    }
  }
  
  const realGainLoss = currentSalary - requiredSalary;
  const realGainLossPercent = (realGainLoss / requiredSalary) * 100;
  const isProfit = realGainLoss >= 0;
  
  return {
    requiredSalary,
    realGainLoss,
    realGainLossPercent,
    isProfit
  };
};

export const formatCurrency = (amount: number, locale: string = 'en-BD'): string => {
  return new Intl.NumberFormat(locale, {
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount);
};

export const formatPercentage = (percentage: number): string => {
  return `${percentage >= 0 ? '+' : ''}${percentage.toFixed(1)}%`;
};