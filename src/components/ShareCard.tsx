import React, { useRef } from 'react';
import html2canvas from 'html2canvas';
import { Download, Share2 } from 'lucide-react';
import { CalculationResult, SalaryInput } from '../types';
import { formatCurrency, formatPercentage } from '../utils/calculations';
import clsx from 'clsx';

interface ShareCardProps {
  result: CalculationResult;
  input: SalaryInput;
  translations: {
    shareResults: string;
    downloadPNG: string;
    appTitle: string;
    requiredSalary: string;
    actualSalary: string;
    realGain: string;
    realLoss: string;
    profit: string;
    loss: string;
  };
}

export const ShareCard: React.FC<ShareCardProps> = ({
  result,
  input,
  translations: t
}) => {
  const shareCardRef = useRef<HTMLDivElement>(null);

  const downloadPNG = async () => {
    if (!shareCardRef.current) return;

    try {
      const canvas = await html2canvas(shareCardRef.current, {
        backgroundColor: '#ffffff',
        scale: 2,
        logging: false,
        useCORS: true,
        allowTaint: true
      });

      const link = document.createElement('a');
      link.download = `salary-analysis-${input.baseYear}-${input.currentYear}.png`;
      link.href = canvas.toDataURL('image/png');
      link.click();
    } catch (error) {
      console.error('Error generating PNG:', error);
    }
  };

  const shareResults = async () => {
    const text = `My purchasing power has ${result.isProfit ? 'increased' : 'decreased'} by ${Math.abs(result.realGainLossPercent).toFixed(1)}% from ${input.baseYear} to ${input.currentYear}. Check your salary against inflation: https://trueworth.mamunhq.com`;
    
    if (navigator.share) {
      try {
        await navigator.share({
          title: t.appTitle,
          text,
          url: window.location.href
        });
      } catch (error) {
        console.log('Share cancelled');
      }
    } else {
      try {
        await navigator.clipboard.writeText(text);
        // Show success feedback
        const button = document.activeElement as HTMLButtonElement;
        const originalText = button.textContent;
        button.textContent = 'Copied!';
        button.style.backgroundColor = '#10B981';
        setTimeout(() => {
          button.textContent = originalText;
          button.style.backgroundColor = '';
        }, 2000);
      } catch (error) {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        
        // Show success feedback
        const button = document.activeElement as HTMLButtonElement;
        const originalText = button.textContent;
        button.textContent = 'Copied!';
        button.style.backgroundColor = '#10B981';
        setTimeout(() => {
          button.textContent = originalText;
          button.style.backgroundColor = '';
        }, 2000);
      }
    }
  };

  return (
    <div className="space-y-6 mt-8">
      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-3">
        <button
          onClick={downloadPNG}
          className={clsx(
            "flex-1 flex items-center justify-center gap-2 px-6 py-3 rounded-xl",
            "bg-blue-600 hover:bg-blue-700 text-white font-medium",
            "transition-colors duration-200"
          )}
        >
          <Download size={20} />
          {t.downloadPNG}
        </button>
        
        <button
          onClick={shareResults}
          className={clsx(
            "flex-1 flex items-center justify-center gap-2 px-6 py-3 rounded-xl",
            "bg-green-600 hover:bg-green-700 text-white font-medium",
            "transition-colors duration-200"
          )}
        >
          <Share2 size={20} />
          {t.shareResults}
        </button>
      </div>

      {/* Share Card (hidden for screenshot) */}
      <div
        ref={shareCardRef}
        className="bg-gradient-to-br from-blue-50 to-purple-50 p-8 rounded-2xl border-2 border-gray-200 mt-6"
        style={{ width: '600px', margin: '0 auto' }}
      >
        <div className="text-center mb-6">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            {t.appTitle}
          </h2>
          <p className="text-gray-600">
            Salary Analysis: {input.baseYear} → {input.currentYear}
          </p>
        </div>

        <div className="grid grid-cols-2 gap-4 mb-6">
          <div className="bg-white rounded-lg p-4 text-center">
            <p className="text-sm text-gray-600 mb-1">{t.requiredSalary}</p>
            <p className="text-xl font-bold text-orange-600">
              ৳{formatCurrency(result.requiredSalary)}
            </p>
          </div>
          <div className="bg-white rounded-lg p-4 text-center">
            <p className="text-sm text-gray-600 mb-1">{t.actualSalary}</p>
            <p className="text-xl font-bold text-blue-600">
              ৳{formatCurrency(input.currentSalary)}
            </p>
          </div>
        </div>

        <div className={clsx(
          "bg-gradient-to-r rounded-lg p-6 text-white text-center",
          result.isProfit 
            ? "from-green-500 to-green-600" 
            : "from-red-500 to-red-600"
        )}>
          <p className="text-lg font-semibold mb-2">
            {result.isProfit ? t.profit : t.loss}
          </p>
          <p className="text-2xl font-bold">
            {formatPercentage(result.realGainLossPercent)}
          </p>
          <p className="text-sm opacity-90 mt-2">
            ৳{formatCurrency(Math.abs(result.realGainLoss))} {result.isProfit ? t.realGain : t.realLoss}
          </p>
        </div>
      </div>
    </div>
  );
};