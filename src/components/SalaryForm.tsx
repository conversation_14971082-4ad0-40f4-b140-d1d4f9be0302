import React, { useState } from 'react';
import { SalaryInput } from '../types';
import { getAvailableYears } from '../utils/inflationData';
import clsx from 'clsx';

interface SalaryFormProps {
  input: SalaryInput;
  setInput: (input: SalaryInput) => void;
  onCalculate: () => void;
  translations: {
    baseYear: string;
    baseMonth: string;
    baseSalary: string;
    currentYear: string;
    currentMonth: string;
    currentSalary: string;
    calculate: string;
    enterAmount: string;
    selectYear: string;
    selectMonth: string;
  };
}

export const SalaryForm: React.FC<SalaryFormProps> = ({
  input,
  setInput,
  onCalculate,
  translations: t
}) => {
  const [showValidation, setShowValidation] = useState(false);
  
  const availableYears = getAvailableYears();
  const currentYearOptions = availableYears.filter(year => year >= input.baseYear);

  const months = [
    { value: 1, label: 'January' },
    { value: 2, label: 'February' },
    { value: 3, label: 'March' },
    { value: 4, label: 'April' },
    { value: 5, label: 'May' },
    { value: 6, label: 'June' },
    { value: 7, label: 'July' },
    { value: 8, label: 'August' },
    { value: 9, label: 'September' },
    { value: 10, label: 'October' },
    { value: 11, label: 'November' },
    { value: 12, label: 'December' }
  ];

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setShowValidation(true);
    
    // Validate required fields
    if (!input.baseSalary || input.baseSalary <= 0) {
      return;
    }
    
    if (!input.currentSalary || input.currentSalary <= 0) {
      return;
    }
    
    if (!input.baseYear || !input.baseMonth || !input.currentYear || !input.currentMonth) {
      return;
    }
    
    // If validation passes, reset validation state and calculate
    setShowValidation(false);
    onCalculate();
  };

  const formatInputValue = (value: number) => {
    return value === 0 ? '' : value.toString();
  };

  const parseInputValue = (value: string) => {
    const cleaned = value.replace(/[^\d.]/g, '');
    return cleaned === '' ? 0 : parseFloat(cleaned) || 0;
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Row 1: Base Year, Base Month, Base Salary */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              {t.baseYear}
            </label>
            <select
              value={input.baseYear}
              onChange={(e) => {
                const newBaseYear = parseInt(e.target.value);
                setInput({
                  ...input,
                  baseYear: newBaseYear,
                  currentYear: Math.max(input.currentYear, newBaseYear),
                  currentMonth: newBaseYear === input.currentYear && input.currentMonth < input.baseMonth ? input.baseMonth : input.currentMonth
                });
              }}
              className={clsx(
                "w-full px-4 py-3 rounded-xl border border-gray-300 dark:border-gray-600",
                "bg-white dark:bg-gray-700 text-gray-900 dark:text-white",
                "focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                "transition-colors duration-200"
              )}
            >
              <option value="">{t.selectYear}</option>
              {availableYears.map(year => (
                <option key={year} value={year}>{year}</option>
              ))}
            </select>
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Month
            </label>
            <select
              value={input.baseMonth}
              onChange={(e) => {
                const newBaseMonth = parseInt(e.target.value);
                setInput({
                  ...input,
                  baseMonth: newBaseMonth,
                  currentMonth: input.baseYear === input.currentYear && newBaseMonth > input.currentMonth ? newBaseMonth : input.currentMonth
                });
              }}
              className={clsx(
                "w-full px-4 py-3 rounded-xl border border-gray-300 dark:border-gray-600",
                "bg-white dark:bg-gray-700 text-gray-900 dark:text-white",
                "focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                "transition-colors duration-200"
              )}
            >
              <option value="">{t.selectMonth}</option>
              {months.map(month => (
                <option key={month.value} value={month.value}>
                  {month.label}
                </option>
              ))}
            </select>
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              {t.baseSalary}
              <span className="text-red-500 ml-1">*</span>
            </label>
            <input
              type="number"
              step="0.01"
              min="0"
              required
              value={formatInputValue(input.baseSalary)}
              onChange={(e) => setInput({
                ...input,
                baseSalary: parseInputValue(e.target.value)
              })}
              placeholder={t.enterAmount}
              className={clsx(
                "w-full px-4 py-3 rounded-xl border border-gray-300 dark:border-gray-600",
                "bg-white dark:bg-gray-700 text-gray-900 dark:text-white",
                "focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                "transition-colors duration-200",
                showValidation && (!input.baseSalary || input.baseSalary <= 0) && "border-red-300 dark:border-red-600"
              )}
            />
            {showValidation && (!input.baseSalary || input.baseSalary <= 0) && (
              <p className="text-red-500 text-xs mt-1">Base salary is required</p>
            )}
          </div>

          {/* Row 2: Current Year, Current Month, Current Salary */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              {t.currentYear}
            </label>
            <select
              value={input.currentYear}
              onChange={(e) => setInput({
                ...input,
                currentYear: parseInt(e.target.value)
              })}
              className={clsx(
                "w-full px-4 py-3 rounded-xl border border-gray-300 dark:border-gray-600",
                "bg-white dark:bg-gray-700 text-gray-900 dark:text-white",
                "focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                "transition-colors duration-200"
              )}
            >
              <option value="">{t.selectYear}</option>
              {currentYearOptions.map(year => (
                <option key={year} value={year}>{year}</option>
              ))}
            </select>
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Month
            </label>
            <select
              value={input.currentMonth}
              onChange={(e) => setInput({
                ...input,
                currentMonth: parseInt(e.target.value)
              })}
              className={clsx(
                "w-full px-4 py-3 rounded-xl border border-gray-300 dark:border-gray-600",
                "bg-white dark:bg-gray-700 text-gray-900 dark:text-white",
                "focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                "transition-colors duration-200"
              )}
            >
              <option value="">{t.selectMonth}</option>
              {months.map(month => (
                <option key={month.value} value={month.value}>
                  {month.label}
                </option>
              ))}
            </select>
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Current Salary (৳)
              <span className="text-red-500 ml-1">*</span>
            </label>
            <input
              type="number"
              step="0.01"
              min="0"
              required
              value={formatInputValue(input.currentSalary)}
              onChange={(e) => setInput({
                ...input,
                currentSalary: parseInputValue(e.target.value)
              })}
              placeholder={t.enterAmount}
              className={clsx(
                "w-full px-4 py-3 rounded-xl border border-gray-300 dark:border-gray-600",
                "bg-white dark:bg-gray-700 text-gray-900 dark:text-white",
                "focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                "transition-colors duration-200",
                showValidation && (!input.currentSalary || input.currentSalary <= 0) && "border-red-300 dark:border-red-600"
              )}
            />
            {showValidation && (!input.currentSalary || input.currentSalary <= 0) && (
              <p className="text-red-500 text-xs mt-1">Current salary is required</p>
            )}
          </div>
        </div>

        <button
          type="submit"
          className={clsx(
            "w-full py-4 px-6 rounded-xl text-white font-medium transition-all duration-200 mt-8",
            "bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700",
            "transform hover:scale-[1.02] active:scale-[0.98]",
            "focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800"
          )}
        >
          {t.calculate}
        </button>
      </form>
    </div>
  );
};