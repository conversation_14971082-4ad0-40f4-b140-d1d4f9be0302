import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  Responsive<PERSON><PERSON><PERSON>,
  <PERSON>
} from 'recharts';
import { CalculationResult, SalaryInput } from '../types';
import { formatCurrency } from '../utils/calculations';

interface SalaryChartProps {
  result: CalculationResult;
  input: SalaryInput;
  translations: {
    chartTitle: string;
    baseYearSalary: string;
    inflationAdjusted: string;
    currentActual: string;
  };
}

export const SalaryChart: React.FC<SalaryChartProps> = ({
  result,
  input,
  translations: t
}) => {
  const data = [
    {
      name: t.baseYearSalary,
      amount: input.baseSalary,
      color: '#3B82F6'
    },
    {
      name: t.inflationAdjusted,
      amount: result.requiredSalary,
      color: '#F59E0B'
    },
    {
      name: t.currentActual,
      amount: input.currentSalary,
      color: result.isProfit ? '#10B981' : '#EF4444'
    }
  ];

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700">
          <p className="text-gray-900 dark:text-white font-medium">{label}</p>
          <p className="text-blue-600 dark:text-blue-400">
            ৳{formatCurrency(payload[0].value)}
          </p>
        </div>
      );
    }
    return null;
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-6">
        {t.chartTitle}
      </h3>
      
      <div className="h-80">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" className="dark:opacity-30" />
            <XAxis 
              dataKey="name"
              tick={{ fontSize: 12 }}
              className="text-gray-600 dark:text-gray-400"
            />
            <YAxis 
              tickFormatter={(value) => `৳${formatCurrency(value)}`}
              tick={{ fontSize: 12 }}
              className="text-gray-600 dark:text-gray-400"
            />
            <Tooltip content={<CustomTooltip />} />
            <Bar 
              dataKey="amount" 
              fill="#3B82F6"
              radius={[8, 8, 0, 0]}
            />
          </BarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};