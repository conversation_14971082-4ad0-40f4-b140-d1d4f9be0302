import React from 'react';
import { CalculationResult, SalaryInput } from '../types';
import { formatCurrency, formatPercentage } from '../utils/calculations';
import { TrendingUp, TrendingDown, DollarSign, Target } from 'lucide-react';
import clsx from 'clsx';

interface ResultsProps {
  result: CalculationResult;
  input: SalaryInput;
  translations: {
    results: string;
    requiredSalary: string;
    actualSalary: string;
    realGain: string;
    realLoss: string;
    profit: string;
    loss: string;
    purchasingPower: string;
    increased: string;
    decreased: string;
    youAreIn: string;
  };
}

export const Results: React.FC<ResultsProps> = ({ result, input, translations: t }) => {
  const { requiredSalary, realGainLoss, realGainLossPercent, isProfit } = result;

  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold text-gray-900 dark:text-white flex items-center gap-2">
        <Target className="text-blue-600 dark:text-blue-400" />
        {t.results}
      </h2>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Required Salary Card */}
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <DollarSign className="text-orange-500" size={20} />
              <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                {t.requiredSalary}
              </span>
            </div>
          </div>
          <div className="mt-2">
            <span className="text-2xl font-bold text-gray-900 dark:text-white">
              ৳{formatCurrency(requiredSalary)}
            </span>
          </div>
        </div>

        {/* Actual Salary Card */}
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <DollarSign className="text-blue-500" size={20} />
              <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                {t.actualSalary}
              </span>
            </div>
          </div>
          <div className="mt-2">
            <span className="text-2xl font-bold text-gray-900 dark:text-white">
              ৳{formatCurrency(input.currentSalary)}
            </span>
          </div>
        </div>

        {/* Real Gain/Loss Amount Card */}
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {isProfit ? (
                <TrendingUp className="text-green-500" size={20} />
              ) : (
                <TrendingDown className="text-red-500" size={20} />
              )}
              <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                {isProfit ? t.realGain : t.realLoss}
              </span>
            </div>
          </div>
          <div className="mt-2">
            <span className={clsx(
              "text-2xl font-bold",
              isProfit ? "text-green-600 dark:text-green-400" : "text-red-600 dark:text-red-400"
            )}>
              ৳{formatCurrency(Math.abs(realGainLoss))}
            </span>
          </div>
        </div>

        {/* Real Gain/Loss Percentage Card */}
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {isProfit ? (
                <TrendingUp className="text-green-500" size={20} />
              ) : (
                <TrendingDown className="text-red-500" size={20} />
              )}
              <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                {isProfit ? t.profit : t.loss}
              </span>
            </div>
          </div>
          <div className="mt-2">
            <span className={clsx(
              "text-2xl font-bold",
              isProfit ? "text-green-600 dark:text-green-400" : "text-red-600 dark:text-red-400"
            )}>
              {formatPercentage(realGainLossPercent)}
            </span>
          </div>
        </div>
      </div>

      {/* Summary Card */}
      <div className={clsx(
        "bg-gradient-to-r rounded-2xl shadow-lg p-6 text-white",
        isProfit 
          ? "from-green-500 to-green-600" 
          : "from-red-500 to-red-600"
      )}>
        <div className="text-center">
          <div className={clsx(
            "inline-flex items-center px-4 py-2 rounded-full text-sm font-medium mb-4",
            isProfit 
              ? "bg-green-100 text-green-800" 
              : "bg-red-100 text-red-800"
          )}>
            {isProfit ? (
              <>
                <TrendingUp size={16} className="mr-1" />
                {t.profit} ({formatPercentage(realGainLossPercent)})
              </>
            ) : (
              <>
                <TrendingDown size={16} className="mr-1" />
                {t.loss} ({formatPercentage(realGainLossPercent)})
              </>
            )}
          </div>
          <p className="text-lg">
            {t.purchasingPower} {isProfit ? t.increased : t.decreased} by{' '}
            <span className="font-bold">{Math.abs(realGainLossPercent).toFixed(1)}%</span>
            {' — '}{t.youAreIn} {isProfit ? t.profit : t.loss}!
          </p>
        </div>
      </div>
    </div>
  );
};