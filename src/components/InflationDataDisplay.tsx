import React from 'react';
import { inflationData } from '../utils/inflationData';
import { Info } from 'lucide-react';

interface InflationDataDisplayProps {
  translations: {
    inflationData: string;
    dataSource: string;
  };
}

export const InflationDataDisplay: React.FC<InflationDataDisplayProps> = ({ translations: t }) => {
  return (
    <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700 p-6">
      <div className="flex items-center gap-2 mb-4">
        <Info size={20} className="text-blue-600 dark:text-blue-400" />
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          {t.inflationData}
        </h3>
      </div>
      
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-6 gap-3 mb-4">
        {inflationData.map((data) => (
          <div
            key={data.year}
            className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
          >
            <div className="font-medium text-gray-900 dark:text-white">
              {data.year}
            </div>
            <div className="text-sm text-blue-600 dark:text-blue-400 font-semibold">
              {data.annual_inflation_pct}%
            </div>
            {data.note && (
              <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                *Projected
              </div>
            )}
          </div>
        ))}
      </div>
      
      <p className="text-xs text-gray-500 dark:text-gray-400 italic">
        {t.dataSource}
      </p>
    </div>
  );
};