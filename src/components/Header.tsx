import React from 'react';
import { Sun, Moon, Globe } from 'lucide-react';
import { Theme, Language } from '../types';
import clsx from 'clsx';

interface HeaderProps {
  theme: Theme;
  toggleTheme: () => void;
  language: Language;
  toggleLanguage: () => void;
  title: string;
  subtitle: string;
  darkModeText: string;
  lightModeText: string;
  languageText: string;
}

export const Header: React.FC<HeaderProps> = ({
  theme,
  toggleTheme,
  language,
  toggleLanguage,
  title,
  subtitle,
  darkModeText,
  lightModeText,
  languageText
}) => {
  return (
    <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 shadow-sm">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex-1">
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white">
              {title}
            </h1>
            <p className="mt-2 text-sm sm:text-base text-gray-600 dark:text-gray-300">
              {subtitle}
            </p>
          </div>
          
          <div className="flex items-center gap-3">
            <button
              onClick={toggleTheme}
              className={clsx(
                "flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors",
                "bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600",
                "text-gray-700 dark:text-gray-300"
              )}
              aria-label={theme === 'dark' ? lightModeText : darkModeText}
            >
              {theme === 'dark' ? <Sun size={16} /> : <Moon size={16} />}
              <span className="hidden sm:inline">
                {theme === 'dark' ? lightModeText : darkModeText}
              </span>
            </button>
            
            <button
              onClick={toggleLanguage}
              className={clsx(
                "flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors",
                "bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600",
                "text-gray-700 dark:text-gray-300"
              )}
              aria-label={`Switch to ${languageText}`}
            >
              <Globe size={16} />
              <span>{languageText}</span>
            </button>
          </div>
        </div>
      </div>
    </header>
  );
};